from pydash import get
from .get_form import get_form_settings


def compute_liveness_integration_flags(applied_form):
    integrations = get_form_settings(applied_form, "ekyc.liveness.integrations", {})

    # Master enable/disable
    integrations_enabled = get(integrations, "enabled", False)
    if not integrations_enabled:
        return {}

    # Check conditions
    condition_settings: list[dict] = get(integrations, "conditions", [])
    final_flags = {}

    for condition_setting in condition_settings:
        name: str = condition_setting.get("name", "")
        conditions: list[dict] = condition_setting.get("conditions", [])
        current_flags: dict = condition_setting.get("flags", {})

        from decision_flow.entity.node_condition import NodeCondition

        updated_answers = {}
        required_answers = []

        def crawl_answers(obj):
            if isinstance(obj, dict):
                if obj.get("category") == "answer":
                    value = obj.get("value")
                    if value:
                        obj.update({"value": f"{{{{answer.{value}}}}}"})
                        required_answers.append(value)
                    return

                for value in obj.values():
                    crawl_answers(value)
            elif isinstance(obj, list):
                for item in obj:
                    crawl_answers(item)

        crawl_answers(conditions)

        more_answers = applied_form.answer_set.filter(question__in=required_answers)
        updated_answers.update({answer.question: answer.value for answer in more_answers})

        context = {
            "enable_lookup_data_point": False,
            "data_point_result": {"answer": updated_answers},
        }

        # Fetch any missing answers from db if needed
        node_condition = NodeCondition(data={}, context=context)
        passed = node_condition.do_compute(data={"groups": [{"children": conditions, "link_operation": "and"}]})
        if passed:
            final_flags = {**final_flags, **current_flags}

        print("compute_liveness_integration_flags", name, conditions, passed, final_flags)

    return final_flags
