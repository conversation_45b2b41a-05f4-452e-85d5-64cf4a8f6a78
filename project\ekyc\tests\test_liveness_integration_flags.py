import pytest
from unittest.mock import Mock, patch, MagicMock
from ekyc.helpers.liveness_integration_flags import compute_liveness_integration_flags


class TestComputeLivenessIntegrationFlags:

    def test_integrations_disabled_returns_empty_dict(self):
        """Test that when integrations are disabled, empty dict is returned"""
        applied_form = Mock()

        with patch("ekyc.helpers.liveness_integration_flags.get_form_settings") as mock_get_form:
            mock_get_form.return_value = {"enabled": False}

            result = compute_liveness_integration_flags(applied_form)

            assert result == {}
            mock_get_form.assert_called_once_with(applied_form, "ekyc.liveness.integrations", {})

    def test_no_integrations_config_returns_empty_dict(self):
        """Test that when no integrations config exists, empty dict is returned"""
        applied_form = Mock()

        with patch("ekyc.helpers.liveness_integration_flags.get_form_settings") as mock_get_form:
            mock_get_form.return_value = {}

            result = compute_liveness_integration_flags(applied_form)

            assert result == {}

    def test_condition_passes_returns_flags(self):
        """Test that when condition passes, flags are returned"""
        applied_form = Mock()

        # Mock answer set
        mock_answer = Mock()
        mock_answer.question = "test"
        mock_answer.value = "abc"
        applied_form.answer_set.filter.return_value = [mock_answer]

        integrations_config = {
            "enabled": True,
            "conditions": [
                {
                    "name": "enable_all",
                    "flags": {"enable_iad": True},
                    "conditions": [
                        {
                            "op": "==",
                            "initial_data": {"value": "test", "category": "answer"},
                            "expected_data": {"value": "abc", "category": "string"},
                        }
                    ],
                }
            ],
        }

        with patch("ekyc.helpers.liveness_integration_flags.get_form_settings") as mock_get_form, patch(
            "decision_flow.entity.node_condition.NodeCondition"
        ) as mock_node_condition:

            mock_get_form.return_value = integrations_config
            mock_condition_instance = Mock()
            mock_condition_instance.do_compute.return_value = True
            mock_node_condition.return_value = mock_condition_instance

            result = compute_liveness_integration_flags(applied_form)

            assert result == {"enable_iad": True}
            applied_form.answer_set.filter.assert_called_once_with(question__in=["test"])

    def test_condition_fails_returns_empty_flags(self):
        """Test that when condition fails, no flags are returned"""
        applied_form = Mock()

        # Mock answer set
        mock_answer = Mock()
        mock_answer.question = "test"
        mock_answer.value = "xyz"  # Different value that won't match
        applied_form.answer_set.filter.return_value = [mock_answer]

        integrations_config = {
            "enabled": True,
            "conditions": [
                {
                    "name": "enable_all",
                    "flags": {"enable_iad": True},
                    "conditions": [
                        {
                            "op": "==",
                            "initial_data": {"value": "test", "category": "answer"},
                            "expected_data": {"value": "abc", "category": "string"},
                        }
                    ],
                }
            ],
        }

        with patch("ekyc.helpers.liveness_integration_flags.get_form_settings") as mock_get_form, patch(
            "decision_flow.entity.node_condition.NodeCondition"
        ) as mock_node_condition:

            mock_get_form.return_value = integrations_config
            mock_condition_instance = Mock()
            mock_condition_instance.do_compute.return_value = False
            mock_node_condition.return_value = mock_condition_instance

            result = compute_liveness_integration_flags(applied_form)

            assert result == {}

    def test_multiple_conditions_merge_flags(self):
        """Test that multiple passing conditions merge their flags"""
        applied_form = Mock()

        # Mock answer set
        mock_answer1 = Mock()
        mock_answer1.question = "test1"
        mock_answer1.value = "abc"
        mock_answer2 = Mock()
        mock_answer2.question = "test2"
        mock_answer2.value = "def"
        applied_form.answer_set.filter.return_value = [mock_answer1, mock_answer2]

        integrations_config = {
            "enabled": True,
            "conditions": [
                {
                    "name": "condition1",
                    "flags": {"flag1": True},
                    "conditions": [
                        {
                            "op": "==",
                            "initial_data": {"value": "test1", "category": "answer"},
                            "expected_data": {"value": "abc", "category": "string"},
                        }
                    ],
                },
                {
                    "name": "condition2",
                    "flags": {"flag2": True},
                    "conditions": [
                        {
                            "op": "==",
                            "initial_data": {"value": "test2", "category": "answer"},
                            "expected_data": {"value": "def", "category": "string"},
                        }
                    ],
                },
            ],
        }

        with patch("ekyc.helpers.liveness_integration_flags.get_form_settings") as mock_get_form, patch(
            "decision_flow.entity.node_condition.NodeCondition"
        ) as mock_node_condition:

            mock_get_form.return_value = integrations_config
            mock_condition_instance = Mock()
            mock_condition_instance.do_compute.return_value = True
            mock_node_condition.return_value = mock_condition_instance

            result = compute_liveness_integration_flags(applied_form)

            assert result == {"flag1": True, "flag2": True}

    def test_crawl_answers_modifies_conditions(self):
        """Test that crawl_answers function properly modifies answer references"""
        applied_form = Mock()
        applied_form.answer_set.filter.return_value = []

        integrations_config = {
            "enabled": True,
            "conditions": [
                {
                    "name": "test_condition",
                    "flags": {"test_flag": True},
                    "conditions": [
                        {
                            "op": "==",
                            "initial_data": {"value": "test_answer", "category": "answer"},
                            "expected_data": {"value": "expected_value", "category": "string"},
                        }
                    ],
                }
            ],
        }

        with patch("ekyc.helpers.liveness_integration_flags.get_form_settings") as mock_get_form, patch(
            "decision_flow.entity.node_condition.NodeCondition"
        ) as mock_node_condition:

            mock_get_form.return_value = integrations_config
            mock_condition_instance = Mock()
            mock_condition_instance.do_compute.return_value = False
            mock_node_condition.return_value = mock_condition_instance

            compute_liveness_integration_flags(applied_form)

            # Verify that the condition was modified to include template syntax
            call_args = mock_condition_instance.do_compute.call_args[1]["data"]
            condition = call_args["groups"][0]["children"][0]
            assert condition["initial_data"]["value"] == "{{answer.test_answer}}"
