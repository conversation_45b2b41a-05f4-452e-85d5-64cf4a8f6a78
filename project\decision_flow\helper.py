import json
from datetime import date, datetime, timedelta
from dateutil.parser import parse
from decision_flow.exceptions import DecisionFlowException
from pydash import parse_int

ALLOW_OPERATIONS = {
    "==": lambda x, y: x == y,
    "!=": lambda x, y: x != y,
    "<": lambda x, y: x < y,
    ">": lambda x, y: x > y,
    "<=": lambda x, y: x <= y,
    ">=": lambda x, y: x >= y,
    "in": lambda x, y: x in y,
    "not_in": lambda x, y: x not in y,
    "is": lambda x, y: x is y,
    "is_not": lambda x, y: x is not y,
    "empty": lambda x, _: x in [None, [], {}, ""],
    "filled": lambda x, _: x not in [None, [], {}, ""],
    "has": lambda x, y: y in x,
    "not_has": lambda x, y: y not in x,
    "starts_with": lambda x, y: x.startswith(y),
    "ends_with": lambda x, y: x.endswith(y),
}

AVAILABLE_OPERATIONS = {
    "boolean": [
        {
            "label": "==",
            "value": "==",
        },
        {
            "label": "!=",
            "value": "!=",
        },
        {
            "label": "<",
            "value": "<",
        },
        {
            "label": ">",
            "value": ">",
        },
        {
            "label": "<=",
            "value": "<=",
        },
        {
            "label": ">=",
            "value": ">=",
        },
        {
            "label": "in",
            "value": "in",
        },
        {
            "label": "not in",
            "value": "not_in",
        },
        {
            "label": "is",
            "value": "is",
        },
        {
            "label": "is not",
            "value": "is_not",
        },
        {
            "label": "empty",
            "value": "empty",
        },
        {
            "label": "filled",
            "value": "filled",
        },
    ],
    "date": [
        {
            "label": "==",
            "value": "==",
        },
        {
            "label": "!=",
            "value": "!=",
        },
        {
            "label": "<",
            "value": "<",
        },
        {
            "label": ">",
            "value": ">",
        },
        {
            "label": "<=",
            "value": "<=",
        },
        {
            "label": ">=",
            "value": ">=",
        },
        {
            "label": "in",
            "value": "in",
        },
        {
            "label": "not in",
            "value": "not_in",
        },
        {
            "label": "is",
            "value": "is",
        },
        {
            "label": "is not",
            "value": "is_not",
        },
        {
            "label": "empty",
            "value": "empty",
        },
        {
            "label": "filled",
            "value": "filled",
        },
    ],
    "json": [
        {
            "label": "==",
            "value": "==",
        },
        {
            "label": "!=",
            "value": "!=",
        },
        {
            "label": "<",
            "value": "<",
        },
        {
            "label": ">",
            "value": ">",
        },
        {
            "label": "<=",
            "value": "<=",
        },
        {
            "label": ">=",
            "value": ">=",
        },
        {
            "label": "in",
            "value": "in",
        },
        {
            "label": "not in",
            "value": "not_in",
        },
        {
            "label": "is",
            "value": "is",
        },
        {
            "label": "is not",
            "value": "is_not",
        },
        {
            "label": "empty",
            "value": "empty",
        },
        {
            "label": "filled",
            "value": "filled",
        },
        {
            "label": "has",
            "value": "has",
        },
        {
            "label": "not has",
            "value": "not_has",
        },
    ],
    "null": [
        {
            "label": "==",
            "value": "==",
        },
        {
            "label": "!=",
            "value": "!=",
        },
        {
            "label": "in",
            "value": "in",
        },
        {
            "label": "not in",
            "value": "not_in",
        },
        {
            "label": "is",
            "value": "is",
        },
        {
            "label": "is not",
            "value": "is_not",
        },
        {
            "label": "empty",
            "value": "empty",
        },
        {
            "label": "filled",
            "value": "filled",
        },
    ],
    "number": [
        {
            "label": "==",
            "value": "==",
        },
        {
            "label": "!=",
            "value": "!=",
        },
        {
            "label": "<",
            "value": "<",
        },
        {
            "label": ">",
            "value": ">",
        },
        {
            "label": "<=",
            "value": "<=",
        },
        {
            "label": ">=",
            "value": ">=",
        },
        {
            "label": "in",
            "value": "in",
        },
        {
            "label": "not in",
            "value": "not_in",
        },
        {
            "label": "is",
            "value": "is",
        },
        {
            "label": "is not",
            "value": "is_not",
        },
        {
            "label": "empty",
            "value": "empty",
        },
        {
            "label": "filled",
            "value": "filled",
        },
    ],
    "string": [
        {
            "label": "==",
            "value": "==",
        },
        {
            "label": "!=",
            "value": "!=",
        },
        {
            "label": "<",
            "value": "<",
        },
        {
            "label": ">",
            "value": ">",
        },
        {
            "label": "<=",
            "value": "<=",
        },
        {
            "label": ">=",
            "value": ">=",
        },
        {
            "label": "in",
            "value": "in",
        },
        {
            "label": "not in",
            "value": "not_in",
        },
        {
            "label": "is",
            "value": "is",
        },
        {
            "label": "is not",
            "value": "is_not",
        },
        {
            "label": "empty",
            "value": "empty",
        },
        {
            "label": "filled",
            "value": "filled",
        },
        {
            "label": "has",
            "value": "has",
        },
        {
            "label": "not has",
            "value": "not_has",
        },
        {
            "label": "starts with",
            "value": "starts_with",
        },
        {
            "label": "ends with",
            "value": "ends_with",
        },
    ],
}


def get_relative_date(target_date: date, extend_days):
    is_integer = type(extend_days) == int
    is_str_digit = type(extend_days) == str and extend_days.isdigit()
    can_parse_int = is_integer or is_str_digit
    if can_parse_int:
        target_date = target_date + timedelta(days=int(extend_days))
    return parse(target_date.isoformat(), ignoretz=True).date()


def parse_value_type(value, value_type, category, applied_form):
    is_relative_date_category = category in ["submission_date", "current_date"]
    lookup_table = {
        "string": str,
        "number": float,
        "boolean": bool,
        "null": None,
        "json": dict,
        "date": date,
        "current_date": date,
        "submission_date": date,
    }
    expected_type = lookup_table.get(value_type)
    if is_relative_date_category:
        expected_type = date

    try:
        if expected_type is None and not is_relative_date_category:
            return None
        if expected_type is date:
            if category == "current_date":
                target_date = datetime.now().date().isoformat()
            elif category == "submission_date":
                target_date = applied_form.submitted_at.date().isoformat()
            else:
                target_date = value

            datetime_obj = parse(target_date, ignoretz=True)
            return datetime_obj.date()
        if expected_type is bool:
            return value in ["True", "true", True, 1]
        if expected_type is dict:
            try:
                return json.loads(value)
            except Exception as e:
                return value
        return expected_type(value)
    except Exception as e:
        _de = DecisionFlowException(f"cannot parse value type {value_type} for {value}")
        _de.e = e
        raise _de


def execute_expression(statement_1, statement_2, op):
    print("execute_expression", statement_1, statement_2, op)
    expression = ALLOW_OPERATIONS.get(op)

    if expression:
        try:
            return expression(statement_1, statement_2)
        except Exception:
            return False
    else:
        raise DecisionFlowException(f"`operation` {op} is invalid.")


def apply_boolean_operation(bool_list, operation):
    result = True
    if operation == "and":
        result = True
        for value in bool_list:
            result = result and value
            if result is False:
                return result
    elif operation == "or":
        result = False
        for value in bool_list:
            result = result or value
            if result is True:
                return result
    else:
        raise DecisionFlowException(f"`operation` {operation} is invalid.")
    return result
