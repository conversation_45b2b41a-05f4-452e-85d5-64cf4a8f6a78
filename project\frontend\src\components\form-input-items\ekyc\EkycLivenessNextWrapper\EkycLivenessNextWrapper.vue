<script setup lang="ts">
import { templateRef } from '@vueuse/core';
import get from 'lodash/get';
import isFinite from 'lodash/isFinite';
import { useI18n } from 'vue-i18n-composable';

import ImageSelector from '@helpers/components/ImageSelector.vue';

import EkycCameraRetryOverlay from '@ekyc/components/EkycCameraRetryOverlay/EkycCameraRetryOverlay.vue';
import EkycPermissionOverlay from '@ekyc/components/EkycPermissionOverlay/EkycPermissionOverlay.vue';
import EkycRetryOverlay from '@ekyc/components/EkycRetryOverlay/EkycRetryOverlay.vue';
import { useDebug } from '@ekyc/composables/liveness/use-debug';
import { useLivenessState } from '@ekyc/composables/liveness/use-liveness-state';
import { useEkycSettings } from '@ekyc/composables/use-ekyc-settings';
import EkycLivenessVNext from '@ekyc/items/liveness/EkycLivenessVNext.vue';
import EkycLivenessVNextIdLive from '@ekyc/items/liveness/EkycLivenessVNextIdLive.vue';
import { createIdliveAttempt } from '@ekyc/services/ekyc';

import EkycMaxAttemptOverlay from '@/components/form-input-items/ekyc/EkycMaxAttemptOverlay/EkycMaxAttemptOverlay.vue';
import { isSuperUser } from '@/helpers/permission-utils';

import EkycContentPass from '../EkycContentPass.vue';
import { EkycProps, useCommonEkycLivenessWrapper } from '../use-common-ekyc-liveness-wrapper';

const LOG_PREFIX = [
  '%c[EkycLivenessNextWrapper]',
  'background: #; color: #aa80aa; border: 1px solid #aa80aa;',
];

const props = defineProps({
  ...EkycProps,
  element: {
    type: Object as () => Types.ISchemaItemEkycLiveness,
    required: true,
    default: () => {},
  },
});
const ekycComponent = templateRef('ekycComponent');

const state = useCommonEkycLivenessWrapper();

const { isActionPassed, isActionFailed } = useLivenessState();

const { t } = useI18n();
const { debugMode } = useDebug();
const showAllDebugButtons = ref(false);

const enableIdLiveFacePlus = ref(!!props.element.iad_pending_url);

const {
  value,
  setting,
  combinedProps,
  passedField,
  passedSchema,
  maxAttemptValue,
  attemptLeft,
  dynamicFormInstance,
  isSandboxEnabled,
  resultState,
  isCameraUsedThisTime,
  overlayPopupState,

  changeRightButtonToOpenCamera,
  callHook,
  setAttemptData,
  // loadEkycResult,
  handleMaxAttempt,

  onVisible,
  handleSendEkycErrorLog,
  capturedHandler,
  closeHandler,
  loadingHandler,

  forceToggleMaxAttempt,
  forceCrossDevice,
  forceEkycResultState,
  checkValueChange,

  loadFromUrl,
  mediaUploadStart,
  mediaUploadSuccess,

  myEntity,
  registerActionRunnerAddons,
} = state;

const isFailedState = computed(() => overlayPopupState.value === 'fail');

function setOverlayPopupState(val: typeof overlayPopupState.value) {
  overlayPopupState.value = val;
}

function retryCamera() {
  setOverlayPopupState('');
  ekycComponent.value?.openVideoCamera?.();
}

function retryLiveness() {
  setOverlayPopupState('');
  ekycComponent.value?.retryLiveness?.();
}

function errorHandler(
  errorBody: Types.EkycRecordErrorPayload | Types.EkycRecordFailPayload = null,
) {
  console.log(...LOG_PREFIX, 'ekyc_error', JSON.stringify(errorBody, null, 2));
  callHook('error');
  dynamicFormInstance.setInputData({
    field: `${props.element.name}_failed`,
    value: true,
  });

  const errorData = get(errorBody, 'error.data', errorBody);
  setAttemptData(errorData);

  if (
    attemptLeft.value <= 0 ||
    errorBody.code === 'error_liveness_max_attempt' ||
    errorBody.reaching_max_attempt
  ) {
    setOverlayPopupState('max_attempt');
    if (!value.value) {
      handleMaxAttempt();
    }
  } else if (errorBody.code === 'error_open_camera_timeout') {
    setOverlayPopupState('camera_retry');
  } else if (errorBody.from === 'init') {
    setOverlayPopupState('camera_permission');
  } else if (errorBody.code === 'error_action_time_out') {
    setOverlayPopupState('timeout');
  } else {
    setOverlayPopupState('fail');
  }

  handleSendEkycErrorLog(errorBody);
}

function uploadedHandler(e: Types.EkycLivenessApiResult) {
  const passMap = e.result || {};

  const passAll = Object.keys(passMap).every(key => {
    state.dynamicFormInstance.setInputData({
      field: `${props.element.name}_${key}_passed`,
      value: passMap[key],
    });
    if (!passMap[key]) {
      const additionalErrorCode: string = passMap[key].error_code;
      const fullKey = (
        additionalErrorCode ? `${key}_${additionalErrorCode.toLowerCase()}` : key
      ) as Types.EkycDocumentValidationTypes;

      console.warn(`${fullKey} not passed`);
      state.setFailedState(fullKey);
    }
    return passMap[key];
  });

  if (!passAll) {
    errorHandler(e as any);
    return;
  }

  state.uploadedHandler(e);
}

function modifyCameraVideoBySchema() {
  let rotateFinal = null;
  const rotateValue = +props.element.camera_rotate_value;
  const rotateField = props.element.camera_rotate_field;
  const rotateAnswer = rotateField ? +get(dynamicFormInstance.dataSource, rotateField) : null;
  if (isFinite(rotateValue)) {
    rotateFinal += rotateValue;
  }
  if (isFinite(rotateAnswer)) {
    rotateFinal += rotateAnswer;
  }
  if (isFinite(rotateFinal)) {
    ekycComponent.value?.rotateCamera(rotateFinal);
  }

  let zoomFinal = null;
  const zoomValue = +props.element.camera_zoom_value;
  const zoomField = props.element.camera_zoom_field;
  const zoomAnswer = zoomField ? +get(dynamicFormInstance.dataSource, zoomField) : null;
  if (isFinite(zoomValue)) {
    zoomFinal += zoomValue;
  }
  if (isFinite(zoomAnswer)) {
    zoomFinal += zoomAnswer;
  }
  if (isFinite(zoomFinal)) {
    ekycComponent.value?.zoomCamera(zoomFinal);
  }

  const flipValue = props.element.camera_flip_value;
  const flipField = props.element.camera_flip_field;
  const flipAnswer: boolean = flipField ? get(dynamicFormInstance.dataSource, flipField) : false;
  if (flipValue || flipAnswer) {
    ekycComponent.value?.flipCamera(flipValue || flipAnswer);
  }

  console.log(
    `Modify camera by schema: 
      rotate=${rotateValue}+${rotateAnswer}=${rotateFinal}
      zoom=${zoomValue}+${zoomAnswer}=${zoomFinal}
      flip=${flipValue}+${flipAnswer}=${flipValue || flipAnswer}`,
  );
}

async function loadEkycResult() {
  const media = 'liveness';
  mediaUploadStart({ media, progress: 100 });
  const res = await loadFromUrl({ loadStatus: true, loadPreview: true });

  if (res.status) {
    isCameraUsedThisTime.value = true;
  }

  checkValueChange();

  mediaUploadSuccess({ media });
  callHook('loaded');
  return res;
}

async function readyUppassHandler() {
  if (!props.element.liveness?.recordTimeMax) {
    const AUTO_SET_TIME_MAX_VALUE = 60 * 1;
    console.log('Auto increase recordTimeMax to:', AUTO_SET_TIME_MAX_VALUE);
    const { allSettings } = useEkycSettings();
    Object.keys(allSettings.liveness.liveness.recordTimeMax).forEach(key => {
      allSettings.liveness.liveness.recordTimeMax[key] = AUTO_SET_TIME_MAX_VALUE;
    });
  }

  dynamicFormInstance.formControl.setItemReady(props.element.name);

  if (isSandboxEnabled.value) {
    changeRightButtonToOpenCamera();
    return;
  }

  if (value.value) {
    await loadEkycResult();
    return;
  }

  if (!ekycComponent.value) {
    return;
  }

  modifyCameraVideoBySchema();

  // NOTE: maxAttemptValue will also ignore for passed attempt, so we need to check number manually
  console.log('attemptLeft: ', attemptLeft.value);
  if (attemptLeft.value <= 0) {
    setOverlayPopupState('max_attempt');
    return;
  }

  ekycComponent.value?.startInit();

  isCameraUsedThisTime.value = true;
}

async function readyIdliveHandler() {
  readyUppassHandler();
}

async function initFailIdliveHandler() {
  enableIdLiveFacePlus.value = false;
  createIdliveAttempt({ success: false, log: { error: 'init_fail' } });
}

onBeforeUnmount(() => {
  /* Call exitPreview - wrapper function of stopPreview({ tryRequestAndStopExternal: false }) */
  ekycComponent.value?.exitPreview();
});

registerActionRunnerAddons([
  {
    entity: myEntity,
    methodMap: { retryLiveness, setOverlayPopupState },
  },
]);

defineExpose({ ekycComponent, overlayPopupState, ...state });
</script>

<template>
  <component :is="element.layout" :element="element" @visible="onVisible">
    <div class="ekyc-main">
      <div id="main-container">
        <EkycLivenessVNextIdLive
          v-if="enableIdLiveFacePlus"
          ref="ekycComponent"
          :setting="setting"
          :disabled="combinedProps.disabled"
          :is-failed-state="isFailedState"
          @ready="readyIdliveHandler"
          @init-fail="initFailIdliveHandler"
          @fail="errorHandler"
          @error="errorHandler"
          @captured="capturedHandler"
          @uploaded="uploadedHandler"
          @close="closeHandler"
          @loading="loadingHandler"
        />

        <EkycLivenessVNext
          v-else
          ref="ekycComponent"
          :setting="setting"
          :disabled="combinedProps.disabled"
          :is-failed-state="isFailedState"
          @ready="readyUppassHandler"
          @fail="errorHandler"
          @error="errorHandler"
          @captured="capturedHandler"
          @uploaded="uploadedHandler"
          @close="closeHandler"
          @loading="loadingHandler"
        />

        <!-- Pass (only after refresh) -->
        <EkycContentPass
          media="liveness"
          item-type="liveness"
          :resultState="resultState"
          :passedField="passedField"
          :passedSchema="passedSchema"
        />

        <!-- Error Camera Permission -->
        <EkycPermissionOverlay
          v-if="overlayPopupState === 'camera_permission'"
          :override-images="element?.override_images"
        />

        <!-- Camera Retry -->
        <EkycCameraRetryOverlay
          v-if="overlayPopupState === 'camera_retry'"
          :override-images="element?.override_images"
          @yes="retryCamera"
        />

        <!-- Retry -->
        <EkycRetryOverlay v-if="overlayPopupState === 'timeout'" @yes="retryLiveness">
          <template #image>
            <img
              v-if="element?.override_images?.['failed-timeout']"
              :src="element?.override_images?.['failed-timeout']"
              class="preview"
              alt="failed"
            />
            <ImageSelector v-else name="failed-timeout" />
          </template>
        </EkycRetryOverlay>
        <EkycRetryOverlay
          v-if="overlayPopupState === 'fail'"
          @yes="retryLiveness"
          style="background-color: white"
        >
          <template #image>
            <img
              v-if="element?.override_images?.['selfie-error']"
              :src="element?.override_images?.['selfie-error']"
              class="preview"
              alt="failed"
            />
            <ImageSelector v-else name="selfie-error" />
          </template>
          <template #title>{{ t('ekyc.recorder.error_liveness_default') }}</template>
          <template #desc>{{ t('ekyc.recorder.error_liveness_default_desc') }}</template>
        </EkycRetryOverlay>

        <!-- Unfocus, Switch App -->
        <EkycRetryOverlay
          v-if="overlayPopupState === 'unfocus_app'"
          @yes="retryLiveness"
          style="background-color: white"
        >
          <template #image>
            <img
              v-if="element?.override_images?.['selfie-error']"
              :src="element?.override_images?.['selfie-error']"
              class="preview"
              alt="failed"
            />
            <ImageSelector v-else name="selfie-error" />
          </template>
          <template #title>{{ t('ekyc.recorder.error_liveness_unfocus_app') }}</template>
          <template #desc>{{ t('ekyc.recorder.error_liveness_unfocus_app_desc') }}</template>
        </EkycRetryOverlay>

        <!-- Max Attempt -->
        <EkycMaxAttemptOverlay
          v-if="overlayPopupState === 'max_attempt'"
          :max-attempt-setting="element.max_attempt_warning"
        />
      </div>
    </div>
    <!-- DEBUG -->
    <div
      v-if="isSuperUser()"
      :class="[
        'debug-overlay fixed z-[100000] bg-black/10 bottom-0 left-0 right-0',
        'hover:opacity-100 transition-all',
        [showAllDebugButtons ? 'opacity-50' : 'opacity-0'],
      ]"
    >
      <div class="flex flex-wrap gap-1 text-xs">
        <button class="button is-primary" @click="showAllDebugButtons = !showAllDebugButtons">
          {}
        </button>
        <template v-if="showAllDebugButtons">
          <button class="button is-primary" @click="enableIdLiveFacePlus = !enableIdLiveFacePlus">
            Mode: {{ enableIdLiveFacePlus ? 'Id Live' : 'Liveness' }}
          </button>
          <button class="button is-primary" @click="debugMode = !debugMode">DEBUG</button>
          <button
            class="button bg-pink-300"
            :class="maxAttemptValue ? 'is-success' : 'is-primary'"
            @click="forceToggleMaxAttempt()"
          >
            Max attempt
          </button>
          <button class="button !bg-blue-300" @click="forceCrossDevice()">Cross Device</button>
          <button class="button !bg-yellow-300" @click="ekycComponent?.startInit()">Restart</button>
          <button
            class="button"
            :class="isActionPassed ? '!bg-green-700' : '!bg-green-400'"
            @click="isActionPassed = !isActionPassed"
          >
            Pass
          </button>
          <button
            class="button"
            :class="isActionFailed ? '!bg-yellow-700' : '!bg-yellow-400'"
            @click="isActionFailed = !isActionFailed"
          >
            Fail
          </button>
          <button
            v-for="st in [
              'hidden',
              'preview',
              'uploading',
              'passed',
              'already_passed',
              'failed_backend',
              'failed_others',
            ]"
            :key="st"
            :class="resultState === st ? '!bg-green-300' : 'bg-white'"
            @click="forceEkycResultState(st)"
          >
            {{ st }}
          </button>
        </template>
      </div>
    </div>
  </component>
</template>

<style lang="scss">
.preview {
  touch-action: manipulation;
}

.debug-overlay .button {
  @apply p-1 h-4;
}
</style>
