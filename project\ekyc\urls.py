from django.urls import path, re_path
from django.conf.urls import include
from rest_framework_nested import routers

from .views.ekyc import EkycViewSet
from .views.liveness import LivenessViewSet
from .views.liveness_iad import LivenessIadViewSet
from .views.back_card import BackCardViewSet
from .views.face_compare import FaceCompareViewSet
from .views.nfc import NfcViewSet
from .views.report_result import ReportResultViewSet
from .views.result import ResultViewSet
from .views.load_models import LoadModelsProxyView
from .views.log import LogViewSet
from .views.documents import AllDocumentViewSets

app_name = "ekyc"

router = routers.DefaultRouter()
router.register(r"", EkycViewSet, basename="ekyc")

ekyc_router = routers.NestedSimpleRouter(router, r"", lookup="ekyc")

ekyc_router.register(r"liveness", LivenessViewSet, basename="liveness")
ekyc_router.register(r"liveness-iad", LivenessIadViewSet, basename="liveness-iad")
ekyc_router.register(r"backcard", BackCardViewSet, basename="back_card")
ekyc_router.register(r"facecompare", FaceCompareViewSet, basename="facecompare")
ekyc_router.register(r"nfc", NfcViewSet, basename="nfc")

ekyc_router.register(r"result", ResultViewSet, basename="result")
ekyc_router.register(r"report-result", ReportResultViewSet, basename="report-result")
ekyc_router.register(r"log", LogViewSet, basename="log")


for _, document_viewset in enumerate(AllDocumentViewSets):
    ekyc_router.register(
        document_viewset.basename,
        document_viewset,
        basename=document_viewset.basename,
    )

urlpatterns = [
    path("", include(router.urls)),
    path("", include(ekyc_router.urls)),
    re_path(r"load/(?P<path>.+)?$", LoadModelsProxyView.as_view(), name="load"),
]
