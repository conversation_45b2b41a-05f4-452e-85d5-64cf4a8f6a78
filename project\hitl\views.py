from django.db import transaction
from django.utils import timezone
from rest_framework import filters, viewsets
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from ekyc.helpers.get_form import get_form_settings
from ekyc.models.liveness import STATUS_FAILED_BACKEND
from hitl.models import HITL
from hitl.pagination import HITLPageNumberPagination
from hitl.serializers import (
    BaseHITLSerializer,
    HITLListSerializer,
    HITLRetrieveSerializer,
    HITLTaskRetrieveSerializer,
    HITLTaskSubmitSerializer,
)


class HITLViewSet(viewsets.ReadOnlyModelViewSet):
    queryset = HITL.objects.all().order_by('-created_at')
    serializer_class = BaseHITLSerializer
    pagination_class = HITLPageNumberPagination
    permission_classes = [IsAuthenticated]
    filter_backends = [filters.SearchFilter]
    search_fields = ['slug', 'form_slug', 'id', 'app_session_id', 'ip_address', 'full_name', 'mrz']

    def get_serializer_class(self):
        serializer_mapping = {
            'list': HITLListSerializer,
            'retrieve': HITLRetrieveSerializer,
            'task_retrieve': HITLTaskRetrieveSerializer,
            'task_submit': HITLTaskSubmitSerializer,
        }

        return serializer_mapping.get(self.action, self.serializer_class)

    def get_queryset(self):
        # for normal actions, return as-is
        if self.action != 'task_retrieve':
            queryset = self.queryset
            query_params = self.request.query_params
            decision_result = query_params.get('decision_result')

            if decision_result:
                queryset = queryset.filter(decision_result=decision_result)

            return queryset

        # for task_retrieve action, filter specific objects
        queryset = HITL.objects.filter(
            decision_result=0,
            decision_maker_user__isnull=True,
            expired_at__gte=timezone.now(),
        ).order_by('expired_at')
        # temporarily store remaining task count in request object
        self.request._remaining_task_count = queryset.count()

        return queryset

    def get_object(self):
        # for normal actions, return as-is
        if self.action != 'task_retrieve':
            return super().get_object()

        # for task_retrieve action, filter specific objects
        user = self.request.user
        queryset = self.filter_queryset(self.get_queryset())

        with transaction.atomic():
            try:
                hitl = queryset.select_for_update(skip_locked=True).first()

                if not hitl:
                    return None

                hitl.decision_maker_user = user
                hitl.save()
            except Exception:
                hitl = None

        return hitl

    def _get_remaining_task_count(self):
        return getattr(self.request, '_remaining_task_count', None)

    def retrieve(self, request, *args, **kwargs):
        query_params = request.query_params
        is_portal = query_params.get('is_portal', '0') == '1'
        hitl = self.get_object()
        serializer = self.get_serializer(hitl)
        expired_at = hitl.expired_at
        decision_result = hitl.decision_result
        current_datetime = timezone.now()
        document_type = hitl.document_type
        liveness_timeout_action = get_form_settings(hitl.slug, 'ekyc.liveness.hitl.timeout_action', 'pass')
        front_card_timeout_action = get_form_settings(hitl.slug, 'ekyc.front_card.hitl.timeout_action', 'pass')

        # if portal request, just return immediately
        if is_portal is True:
            return Response(serializer.data)

        # if ignore timeout for liveness, just return immediately
        if document_type == 'liveness' and liveness_timeout_action == 'pass':
            return Response(serializer.data)

        # if ignore timeout for frontcard, just return immediately
        if document_type != 'liveness' and front_card_timeout_action == 'pass':
            return Response(serializer.data)

        # if hitl is expired and still pending review, mark related ekyc as failed
        if expired_at < current_datetime and decision_result == 0:
            if document_type == 'liveness':
                liveness = hitl.liveness
                liveness.status = STATUS_FAILED_BACKEND
                liveness.save()
            else:
                frontcard = hitl.frontcard
                frontcard.is_success = False
                frontcard.save()

        return Response(serializer.data)

    @action(detail=False, methods=['get'], url_path=r'task')
    def task_retrieve(self, request):
        hitl = self.get_object()

        if not hitl:
            response_data = {}
        else:
            serializer = self.get_serializer(hitl)
            response_data = serializer.data

        return Response(response_data, status=200)

    @action(detail=True, methods=['post'], url_path=r'submit')
    def task_submit(self, request, pk=None):
        hitl = self.get_object()
        serializer = self.get_serializer(hitl, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)
        serializer.save()

        # after submission, try to get next task immediately
        self.action = 'task_retrieve'
        return self.task_retrieve(request)
