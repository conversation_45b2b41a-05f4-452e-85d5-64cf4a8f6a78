from rest_framework.reverse import reverse
from ekyc.helpers.build_url_from_settings import build_url_from_settings
from ekyc.helpers.liveness_integration_flags import compute_liveness_integration_flags


"""
{
    "name": "ekyc_item_1",
    "layout": "DefaultWrapper",
    "display": {
        "label": "ตรวจสอบ Liveness ของใบหน้า"
    },
    "configs": {
        "error_template": {
            "section": true,
            "item": false
        }
    },
    "validator_rule": "required",
    "type": "Ekyc.Liveness",
    "uploadOnCaptured": true,
    "liveness": {
        "modelType": "tiny"
    },
    "face_actions": "https://dev.demo.creditok.co/api/forms/th/credit-loan/TGzPb/ekyc/faceactions",
    "upload_url": "https://dev.demo.creditok.co/api/forms/th/credit-loan/TGzPb/ekyc/liveness/images"
}
"""


class LivenessBase:
    def build(self, key, json, applied_form, request=None, **kwargs):
        if not applied_form:
            return {key: json}

        json["face_actions_url"] = (
            # build_url_from_settings(
            #     applied_form=applied_form,
            #     setting_path="ekyc.liveness.face_actions_api_host",
            #     url_name="ekyc:liveness-faceaction",
            # )
            reverse("ekyc:liveness-faceaction", kwargs={"ekyc_ref": applied_form.slug}, request=request)
            + f"?item={key}"
        )
        json["log_url"] = build_url_from_settings(
            applied_form=applied_form,
            setting_path="ekyc.liveness.log_api_host",
            url_name="ekyc:log-post",
        )
        return {key: json}

    def is_item_completed(self, applied_form, **kwargs):
        from ekyc.models import Liveness

        return Liveness.objects.filter(ekyc__ref=applied_form.slug).exists()


class Liveness(LivenessBase):
    pass


class LivenessV1(LivenessBase):
    pass


class LivenessV2(LivenessBase):
    pass


class LivenessV3(LivenessBase):
    pass


class LivenessV4(LivenessBase):
    pass


class LivenessVNext(LivenessBase):
    def build(self, key, json, applied_form, request=None, **kwargs):
        key_json = super().build(key, json, applied_form, request, **kwargs)
        json = key_json[key]

        integration_flags = compute_liveness_integration_flags(applied_form)
        should_include_iad = integration_flags.get("enable_iad", False)
        if should_include_iad:
            slug = applied_form.slug
            json["iad_pending_url"] = reverse("ekyc:liveness-iad-pending", kwargs={"ekyc_ref": slug}, request=request)

        return {key: json}
